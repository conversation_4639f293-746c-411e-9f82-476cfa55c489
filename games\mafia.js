const {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ActionRow<PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ButtonStyle,
    ChannelType,
    StringSelectMenuBuilder,
    PermissionsBitField,
  } = require('discord.js');

  const config = require('../config.js');

  // Game state
  let gameState = {
    players: [],
    allPlayers: [],
    playerRoles: new Map(),
    mafias: [],
    doctor: null,
    detector: null,
    bodyguard: null,
    mayor: null,
    president: null,
    presidentUsedAbility: false,
    gameActive: false,
    protectedPlayer: null,
    shieldedPlayer: null,
    shieldedPlayerRound: null,
    killedPlayer: null,
    votes: new Map(),
    skipVotes: 0,
    totalVotes: 0,
    mafiaActions: new Map(),
    doctorActionTaken: false,
    doctorPhaseEnded: false,
    detectorUsedAbility: false,
    bodyguardUsedAbility: false,
    bodyguardPhaseEnded: false,
    gameMessage: null,
    mafiaMessages: new Map(),
    mafiaInteractions: new Map(),
    doctorInteraction: null,
    detectorInteraction: null,
    bodyguardInteraction: null,
    mayorInteraction: null,
    votePhaseActive: false,
    mafiaPhaseEnded: false,
    mafiaTimeout: null,
    currentRound: 0,
    mafiaThread: null,
  };

  const interactions = new Map();
  let gameInterval = null;
  let gameTimeouts = [];

  // Load allowedRoleIds from config.json
  const { allowedRoleIds } = require('../config.json');
  const { pointsManager } = require('../points.js');

  // Game configuration from unified config
  const mafiaConfig = {
    allowedRoleIds: allowedRoleIds,
    startTime: config.mafia.startTime,
    mafiaKillTime: config.mafia.mafiaKillTime,
    docActionTime: config.mafia.docActionTime,
    detectorPhaseTime: config.mafia.detectorPhaseTime,
    citizenVoteTime: config.mafia.citizenVoteTime,
    bodyguardPhaseTime: config.mafia.bodyguardPhaseTime,
    maxPlayers: config.mafia.maxPlayers,
    minPlayers: config.mafia.minPlayers,
    embedColor: config.mafia.embedColor
  };

  async function handleMafiaGame(message) {
    try {
      const member = message.member;

      // Check if user has any of the allowed roles
      const hasPermission = mafiaConfig.allowedRoleIds.some(roleId => member.roles.cache.has(roleId));
      if (!hasPermission) {
        await message.reply('❌ **ليس لديك الإذن لبدء اللعبة.**');
        return;
      }

      if (gameState.gameActive) {
        await message.channel.send('⚠️ **اللعبة جارية بالفعل.**');
        return;
      }

      await startGame(message);
    } catch (error) {
      console.error('Error in handleMafiaGame:', error);
      await message.channel.send('❌ **حدث خطأ غير متوقع أثناء معالجة الرسالة.**');
    }
  }

  async function startGame(message) {
    try {
      resetGame();

      gameState.gameActive = true;
      gameState.allPlayers = [];

      const embed = new EmbedBuilder()
        .setTitle('🔥 **لعبة مافيا** 🔥')
        .setDescription(
          `اضغط على الزر أدناه للانضمام إلى اللعبة.\n\nستبدأ اللعبة في ${mafiaConfig.startTime / 1000} ثوانٍ.`
        )
        .setColor(mafiaConfig.embedColor)
        .addFields(
          {
            name: 'عدد اللاعبين',
            value: `0/${mafiaConfig.maxPlayers}`,
            inline: true,
          },
          {
            name: 'الوقت المتبقي',
            value: `${mafiaConfig.startTime / 1000} ثواني`,
            inline: true,
          },
          {
            name: 'اللاعبين المنضمين',
            value: 'لا يوجد لاعبون حتى الآن.',
          }
        )
        .setFooter({ text: 'انضم الآن واستمتع باللعبة!' })
        .setTimestamp();

      const row = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId('join_game')
          .setLabel('انضم إلى اللعبة')
          .setStyle(ButtonStyle.Success),
        new ButtonBuilder()
          .setCustomId('leave_game')
          .setLabel('مغادرة اللعبة')
          .setStyle(ButtonStyle.Danger),
        new ButtonBuilder()
          .setCustomId('game_info')
          .setLabel('شرح')
          .setStyle(ButtonStyle.Secondary)
      );

      gameState.gameMessage = await message.channel.send({
        embeds: [embed],
        components: [row],
      });

      let timeLeft = mafiaConfig.startTime / 1000;
      gameInterval = setInterval(async () => {
        try {
          timeLeft--;

          const joinedPlayers = gameState.players.length
            ? gameState.players.map((id) => `<@${id}>`).join(', ')
            : 'لا يوجد لاعبون حتى الآن.';

          const allPlayers = gameState.allPlayers.length
            ? gameState.allPlayers.map((id) => `<@${id}>`).join(', ')
            : 'لا يوجد لاعبون حتى الآن.';

          const updatedEmbed = EmbedBuilder.from(embed)
            .setFields(
              {
                name: 'عدد اللاعبين',
                value: `${gameState.players.length}/${config.maxPlayers}`,
                inline: true,
              },
              {
                name: 'الوقت المتبقي',
                value: `${timeLeft} ثواني`,
                inline: true,
              },
              {
                name: 'اللاعبين المنضمين',
                value: joinedPlayers,
              }
            )
            .setDescription(
              `اضغط على الزر أدناه للانضمام إلى اللعبة.\n\nستنطلق اللعبة قريبًا!`
            );

          if (timeLeft <= 0) {
            clearInterval(gameInterval);
            gameInterval = null;

            const disabledRow = new ActionRowBuilder().addComponents(
              new ButtonBuilder()
                .setCustomId('join_game')
                .setLabel('انضم إلى اللعبة')
                .setStyle(ButtonStyle.Success)
                .setDisabled(true),
              new ButtonBuilder()
                .setCustomId('leave_game')
                .setLabel('مغادرة اللعبة')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true)
            );

            if (gameState.gameMessage) {
              await gameState.gameMessage.edit({
                embeds: [updatedEmbed],
                components: [disabledRow],
              }).catch((error) => {
                console.error('Error editing game message:', error);
                gameState.gameMessage = null;
              });
            }

            if (gameState.players.length >= mafiaConfig.minPlayers) {
              await assignRoles(message.channel);
            } else {
              gameState.gameActive = false;
              await message.channel.send('❌ **لم ينضم عدد كافٍ من اللاعبين. تم إلغاء اللعبة.**');
              resetGame();
            }
          } else {
            if (gameState.gameMessage) {
              await gameState.gameMessage.edit({ embeds: [updatedEmbed], components: [row] }).catch((error) => {
                console.error('Error editing game message:', error);
                gameState.gameMessage = null;
              });
            }
          }
        } catch (error) {
          console.error('Error in game interval:', error);
        }
      }, 1000);
    } catch (error) {
      console.error('Error in startGame:', error);
      await message.channel.send('❌ **حدث خطأ أثناء بدء اللعبة.**');
    }
  }

  // Setup interaction handlers
  function setupMafiaInteractions(client) {
    client.on('interactionCreate', async (interaction) => {
      try {
        if (!interaction.isButton()) return;

        const { customId } = interaction;

        if (customId === 'join_game') {
          if (gameState.players.length >= mafiaConfig.maxPlayers) {
            await interaction.reply({
              content: '❌ **تم الوصول إلى الحد الأقصى من اللاعبين.**',
              ephemeral: true,
            });
            return;
          }

          if (!gameState.players.includes(interaction.user.id)) {
            gameState.players.push(interaction.user.id);
            if (!gameState.allPlayers.includes(interaction.user.id)) {
              gameState.allPlayers.push(interaction.user.id);
            }
            interactions.set(interaction.user.id, interaction);
            await interaction.reply({
              content: '✅ **لقد انضممت إلى اللعبة!**',
              ephemeral: true,
            });
          } else {
            await interaction.reply({
              content: '❌ **أنت بالفعل في اللعبة!**',
              ephemeral: true,
            });
          }
        } else if (customId === 'leave_game') {
          if (gameState.players.includes(interaction.user.id)) {
            gameState.players = gameState.players.filter((id) => id !== interaction.user.id);
            await interaction.reply({
              content: '❌ **لقد غادرت اللعبة.**',
              ephemeral: true,
            });
          } else {
            await interaction.reply({
              content: '❌ **أنت لست في اللعبة.**',
              ephemeral: true,
            });
          }
        } else if (customId === 'game_info') {
          await interaction.reply({
            content: `**🎮 شرح لعبة المافيا**

  في عندك مافيا و طبيب ومحقق وحارس وعمدة ورئيس,
  عدد مافيا اقل من 8 يكون 1 مافيا و8 وفوق يكون 2 مافيا 15 لاعل يكون 3 مافيا و 23 لاعب يكون 4 مافيا

  **🩺 الطبيب:** يحمي من القتل نفس الطبيعي

  **🕵️ المحقق:** له فرصه وحده بس للعبة كلها يطلع رول حق شخص واحد يختاره او يعمله سكب عادي

  **🛡️ الحارس:** يعطي درع لشخص واحد يختاره ومايطلع اسمه للمافيا بالدور الجاي

  **👑 العمدة:** من يصوت مع المواطنين كل صوت له ينحسب صوتين مرتين يعني

  **🎖️ الرئيس:** له فرصه وحده يقلب الاصوات حق المواطنين على شخص واحد ببآله

  واذا كان عدد المافيا 2 وفوق البوت تلقائي يعملهم ثريد (شات خاص) لهم يتناقشون فيه مين يقتلون والخ ..`,
            ephemeral: true,
          });
        } else if (customId.startsWith('kill_')) {
          await handleMafiaKill(interaction);
        } else if (customId.startsWith('protect_')) {
          await handleDoctorProtect(interaction);
        } else if (customId.startsWith('detect_')) {
          await handleDetectorDetect(interaction);
        } else if (customId === 'skip_detect') {
          await handleDetectorSkip(interaction);
        } else if (customId.startsWith('shield_')) {
          await handleBodyguardShield(interaction);
        } else if (customId === 'skip_shield') {
          await handleBodyguardSkip(interaction);
        } else if (customId.startsWith('vote_')) {
          await handleVote(interaction);
        } else if (customId === 'skip_vote') {
          await handleSkipVote(interaction);
        } else if (customId === 'president_ability') {
          await handlePresidentAbility(interaction);
        } else if (customId.startsWith('president_select_')) {
          await handlePresidentSelection(interaction);
        }
      } catch (error) {
        console.error('Error in interactionCreate:', error);
        if (!interaction.replied) {
          await interaction.reply({
            content: '❌ **حدث خطأ غير متوقع. حاول مرة أخرى.**',
            ephemeral: true,
          });
        }
      }
    });
  }

  async function assignRoles(channel) {
    try {
      if (!gameState.gameActive) return;

      gameState.allPlayers = [...gameState.players];

      const shuffledPlayers = gameState.players.sort(() => Math.random() - 0.5);

      if (shuffledPlayers.length < 6) {
        await channel.send('❌ **عدد اللاعبين غير كافٍ لتعيين جميع الأدوار. تحتاج على الأقل إلى 6 لاعبين.**');
        resetGame();
        return;
      }

      let mafiaCount = 1;
      if (shuffledPlayers.length >= 8) {
        mafiaCount = 2;
      }
      if (shuffledPlayers.length >= 15) {
        mafiaCount = 3;
      }
      if (shuffledPlayers.length >= 23) {
        mafiaCount = 4;
      }

      gameState.mafias = shuffledPlayers.slice(0, mafiaCount);
      gameState.doctor = shuffledPlayers[mafiaCount];
      gameState.detector = shuffledPlayers[mafiaCount + 1];
      gameState.bodyguard = shuffledPlayers[mafiaCount + 2];
      gameState.mayor = shuffledPlayers[mafiaCount + 3];
      gameState.president = shuffledPlayers[mafiaCount + 4];

      shuffledPlayers.slice(mafiaCount + 5).forEach((player) => {
        gameState.playerRoles.set(player, 'مواطن');
      });

      for (const mafia of gameState.mafias) {
        gameState.playerRoles.set(mafia, 'مافيا');
      }
      gameState.playerRoles.set(gameState.doctor, 'طبيب');
      gameState.playerRoles.set(gameState.detector, 'محقق');
      gameState.playerRoles.set(gameState.bodyguard, 'حارس شخصي');
      gameState.playerRoles.set(gameState.mayor, 'عمدة');
      gameState.playerRoles.set(gameState.president, 'رئيس');

      for (const playerId of gameState.players) {
        const role = gameState.playerRoles.get(playerId);
        const interaction = interactions.get(playerId);

        if (interaction) {
          if (!interaction.replied) {
            await interaction.deferReply({ ephemeral: true }).catch((error) => {
              console.error(`Error deferring interaction for player ${playerId}:`, error);
            });
          }
          await interaction.followUp({
            ephemeral: true,
            content: `🎭 **دورك هو:** **${role.toUpperCase()}**.`,
          }).catch((error) => {
            console.error(`Error sending role to player ${playerId}:`, error);
          });
        } else {
          console.error(`Interaction for player ${playerId} not found.`);
        }
      }

      if (gameState.mafias.length >= 2) {
        try {
          const mafiaThread = await channel.threads.create({
            name: `Mafia Chat - Game ${gameState.currentRound}`,
            autoArchiveDuration: 60,
            type: ChannelType.PrivateThread,
            invitable: false,
          });

          for (const mafiaId of gameState.mafias) {
            await mafiaThread.members.add(mafiaId).catch((error) => {
              console.error(`Error adding mafia member ${mafiaId} to thread:`, error);
            });
          }

          gameState.mafiaThread = mafiaThread;

          const mafiaMentions = gameState.mafias.map(id => `<@${id}>`).join(', ');

          await mafiaThread.send(`${mafiaMentions}\n💀 **هذا هو الشات الخاص بالمافيا. يمكنك مناقشة خططك هنا.**`);
        } catch (error) {
          console.error('Error creating mafia thread:', error);
          await channel.send('❌ **حدث خطأ أثناء إنشاء الشات الخاص بالمافيا.**');
        }
      }

      const embed = new EmbedBuilder()
        .setTitle('📋 **تقرير اللاعبين**')
        .setDescription('**تم توزيع الأدوار على اللاعبين. إليكم تفاصيل اللعبة:**')
        .setColor('#1E90FF')
        .addFields(
          { name: '👥 **عدد اللاعبين**', value: `${gameState.players.length}`, inline: true },
          { name: '💀 **عدد المافيا**', value: `${mafiaCount}`, inline: true },
          { name: '💉 **عدد الأطباء**', value: `1`, inline: true },
          { name: '🕵️‍♂️ **عدد المحققين**', value: `1`, inline: true },
          { name: '🛡️ **عدد الحراس الشخصيين**', value: `1`, inline: true },
          { name: '👑 **عدد العمدة**', value: `1`, inline: true },
          { name: '👨‍🌾 **عدد المواطنين**', value: `${gameState.players.length - mafiaCount - 4}`, inline: true },
          {
            name: 'جميع اللاعبين',
            value: gameState.allPlayers.map(id => `<@${id}>`).join(', ') || 'لا يوجد لاعبون حتى الآن.',
            inline: false
          }
        )
        .setFooter({ text: 'حظًا موفقًا للجميع!' })
        .setTimestamp();

      await channel.send({ embeds: [embed] });

      await channel.send('🚨 **تم الكشف عن الأدوار لجميع اللاعبين. ستبدأ اللعبة في 5 ثواني.**');

      const timeout = setTimeout(() => startMafiaPhase(channel), 5000);
      gameTimeouts.push(timeout);
    } catch (error) {
      console.error('Error in assignRoles:', error);
      await channel.send('❌ **حدث خطأ أثناء تعيين الأدوار.**');
    }
  }

  function resetGame() {
    if (gameState.gameMessage) {
      disableButtons(gameState.gameMessage);
    }

    if (gameState.mafiaThread) {
      try {
        gameState.mafiaThread.delete().catch((error) => {
          console.error('Error deleting mafia thread:', error);
        });
        gameState.mafiaThread = null;
      } catch (error) {
        console.error('Error deleting mafia thread:', error);
      }
    }

    gameState = {
      players: [],
      allPlayers: [],
      playerRoles: new Map(),
      mafias: [],
      doctor: null,
      detector: null,
      bodyguard: null,
      mayor: null,
      gameActive: false,
      protectedPlayer: null,
      shieldedPlayer: null,
      shieldedPlayerRound: null,
      killedPlayer: null,
      votes: new Map(),
      skipVotes: 0,
      totalVotes: 0,
      mafiaActions: new Map(),
      doctorActionTaken: false,
      doctorPhaseEnded: false,
      detectorUsedAbility: false,
      bodyguardUsedAbility: false,
      bodyguardPhaseEnded: false,
      gameMessage: null,
      mafiaMessages: new Map(),
      mafiaInteractions: new Map(),
      doctorInteraction: null,
      detectorInteraction: null,
      bodyguardInteraction: null,
      mayorInteraction: null,
      votePhaseActive: false,
      mafiaPhaseEnded: false,
      mafiaTimeout: null,
      currentRound: 0,
      mafiaThread: null,
    };

    interactions.clear();

    if (gameInterval) {
      clearInterval(gameInterval);
      gameInterval = null;
    }

    gameTimeouts.forEach((timeout) => clearTimeout(timeout));
    gameTimeouts = [];

    console.log('Game state has been reset.');
  }

  async function disableButtons(message) {
    if (!message) return;
    try {
      const fetchedMessage = await message.fetch().catch((error) => {
        if (error.code === 10008) {
          console.error('Message was deleted before it could be fetched.');
          return null;
        } else {
          throw error;
        }
      });

      if (!fetchedMessage) return;

      const disabledComponents = fetchedMessage.components.map((row) => {
        return new ActionRowBuilder().addComponents(
          row.components.map((button) =>
            ButtonBuilder.from(button).setDisabled(true)
          )
        );
      });

      await fetchedMessage.edit({ components: disabledComponents }).catch((error) => {
        console.error('Error editing message to disable buttons:', error);
      });
    } catch (error) {
      if (error.code === 10008) {
        console.error('Error: Tried to disable buttons on a message that no longer exists.');
      } else {
        console.error('Error while disabling buttons:', error);
      }
    }
  }

  async function disableButtonsInChannel(channel) {
    try {
      const messages = await channel.messages.fetch({ limit: 10 });
      for (const message of messages.values()) {
        if (message.components && message.components.length > 0) {
          await disableButtons(message).catch(error => {
            console.error('Error disabling buttons in channel:', error);
          });
        }
      }
    } catch (error) {
      console.error('Error fetching messages to disable buttons:', error);
    }
  }

  // Helper function to create button rows
  function createButtonRows(buttons) {
    const rows = [];
    for (let i = 0; i < buttons.length; i += 5) {
      rows.push(new ActionRowBuilder().addComponents(buttons.slice(i, Math.min(i + 5, buttons.length))));
    }
    return rows;
  }

  // Placeholder functions for the game mechanics
  // These would need to be implemented with the full game logic
  async function startMafiaPhase(channel) {
    await channel.send('💀 **بدأت مرحلة المافيا...**');
    // Implementation would go here
  }

  async function handleMafiaKill(interaction) {
    // Implementation would go here
  }

  async function handleDoctorProtect(interaction) {
    // Implementation would go here
  }

  async function handleDetectorDetect(interaction) {
    // Implementation would go here
  }

  async function handleDetectorSkip(interaction) {
    // Implementation would go here
  }

  async function handleBodyguardShield(interaction) {
    // Implementation would go here
  }

  async function handleBodyguardSkip(interaction) {
    // Implementation would go here
  }

  async function handleVote(interaction) {
    // Implementation would go here
  }

  async function handleSkipVote(interaction) {
    // Implementation would go here
  }

  async function handlePresidentAbility(interaction) {
    // Implementation would go here
  }

  async function handlePresidentSelection(interaction) {
    // Implementation would go here
  }

  async function tallyVotes(channel) {
    // Implementation would go here
  }

  function checkWinConditions(channel) {
    // Implementation would go here
    return false;
  }

  function getAlivePlayers() {
    if (gameState.players.length === 0) return 'لا يوجد أحياء.';
    return gameState.players.map((id) => `<@${id}>`).join(', ');
  }

  async function resolveNightPhase(channel) {
    // Implementation would go here
  }

  async function startDoctorPhase(channel) {
    // Implementation would go here
  }

  async function startBodyguardPhase(channel) {
    // Implementation would go here
  }

  async function startDetectorPhase(channel) {
    // Implementation would go here
  }

  async function startVotePhase(channel) {
    // Implementation would go here
  }

  module.exports = {
    handleMafiaGame,
    setupMafiaInteractions,
    resetGame,
    isGameActive: () => gameState.gameActive
  };
