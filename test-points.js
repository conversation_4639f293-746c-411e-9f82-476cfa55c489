// Simple test for the points system
const { points<PERSON>anager, ABILITY_COST, WIN_REWARD } = require('./points.js');

async function testPointsSystem() {
  console.log('🧪 Testing Points System...\n');

  const testUserId = '123456789';
  
  try {
    // Test 1: Initial points
    console.log('Test 1: Initial points');
    let points = pointsManager.getPoints(testUserId);
    console.log(`Initial points: ${points}`);
    console.log(`Expected: 0, Got: ${points}, ${points === 0 ? '✅ PASS' : '❌ FAIL'}\n`);

    // Test 2: Award win
    console.log('Test 2: Award win');
    const newPoints = await pointsManager.awardWin(testUserId);
    console.log(`Points after win: ${newPoints}`);
    console.log(`Expected: ${WIN_REWARD}, Got: ${newPoints}, ${newPoints === WIN_REWARD ? '✅ PASS' : '❌ FAIL'}\n`);

    // Test 3: Check stats
    console.log('Test 3: Check stats');
    const stats = pointsManager.getStats(testUserId);
    console.log(`Stats:`, stats);
    console.log(`Games played: ${stats.gamesPlayed}, Wins: ${stats.wins}, Win rate: ${stats.winRate}%`);
    console.log(`Expected: 1 game, 1 win, 100% win rate, ${stats.gamesPlayed === 1 && stats.wins === 1 && stats.winRate === 100 ? '✅ PASS' : '❌ FAIL'}\n`);

    // Test 4: Check ability usage
    console.log('Test 4: Check ability usage');
    const canUseAbility = pointsManager.hasEnoughPoints(testUserId, ABILITY_COST);
    console.log(`Can use ability (costs ${ABILITY_COST}): ${canUseAbility}`);
    console.log(`Expected: false (only has ${WIN_REWARD} points), Got: ${canUseAbility}, ${!canUseAbility ? '✅ PASS' : '❌ FAIL'}\n`);

    // Test 5: Add more points
    console.log('Test 5: Add more points');
    await pointsManager.addPoints(testUserId, 5);
    const totalPoints = pointsManager.getPoints(testUserId);
    console.log(`Points after adding 5: ${totalPoints}`);
    console.log(`Expected: ${WIN_REWARD + 5}, Got: ${totalPoints}, ${totalPoints === WIN_REWARD + 5 ? '✅ PASS' : '❌ FAIL'}\n`);

    // Test 6: Use ability
    console.log('Test 6: Use ability');
    const abilityUsed = await pointsManager.useAbility(testUserId);
    const pointsAfterAbility = pointsManager.getPoints(testUserId);
    console.log(`Ability used successfully: ${abilityUsed}`);
    console.log(`Points after using ability: ${pointsAfterAbility}`);
    console.log(`Expected: true, ${WIN_REWARD + 5 - ABILITY_COST} points, Got: ${abilityUsed}, ${pointsAfterAbility}, ${abilityUsed && pointsAfterAbility === WIN_REWARD + 5 - ABILITY_COST ? '✅ PASS' : '❌ FAIL'}\n`);

    // Test 7: Record loss
    console.log('Test 7: Record loss');
    await pointsManager.recordLoss(testUserId);
    const finalStats = pointsManager.getStats(testUserId);
    console.log(`Final stats:`, finalStats);
    console.log(`Expected: 2 games, 1 win, 1 loss, 50% win rate`);
    console.log(`Got: ${finalStats.gamesPlayed} games, ${finalStats.wins} wins, ${finalStats.losses} losses, ${finalStats.winRate}% win rate`);
    console.log(`${finalStats.gamesPlayed === 2 && finalStats.wins === 1 && finalStats.losses === 1 && finalStats.winRate === 50 ? '✅ PASS' : '❌ FAIL'}\n`);

    console.log('🎉 Points system test completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testPointsSystem();
